package com.jinghang.capital.core.banks.hxbk.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * 湖消蚂蚁天枢配置
 *
 * @Author: Lior
 * @CreateTime: 2025/7/7 20:14
 */
@Configuration
@RefreshScope
public class HXBKConfig {

    @Value("${hxbk.server.baseUrl}")
    private String baseUrl;

    @Value("${hxbk.server.ak}")
    private String ak;

    @Value("${hxbk.server.sk}")
    private String sk;

    @Value("${hxbk.loan.start}")
    private String loanStartTime;

    @Value("${hxbk.loan.end}")
    private String loanEndTime;

    @Value("${hxbk.callback.partner.privateKey}")
    private String partnerPrivateKey;

    @Value("${hxbk.callback.ant.publicKey}")
    private String antPublicKey;

    @Value("${hxbk.credit.fundCode}")
    private String fundCode;

    @Value("${hxbk.credit.rate}")
    private Double creditRate;

    /**
     * 湖消蚂蚁平台商户号
     */
    @Value("${hxbk.merchant.shop}")
    private String merchantShop;

    /**
     * 湖消年结期间开始时间
     */
    @Value("${hxbk.year.period.start.time}")
    private String yearPeriodStartTime;

    /**
     * 湖消年结期间结束时间
     */
    @Value("${hxbk.year.period.end.time}")
    private String yearPeriodEndTime;

    /**
     * 湖消还款黑暗期开始时间
     */
    @Value("${hxbk.repay.dark.period.start.time}")
    private String repayDarkPeriodStartTime;

    /**
     * 湖消还款黑暗期结束时间
     */
    @Value("${hxbk.repay.dark.period.end.time}")
    private String repayDarkPeriodEndTime;

    /**
     * 湖消还款超时时间配置
     */
    @Value("${hxbk.repay.timeout.conf}")
    private String repayTimeoutConf;

    /**
     * 湖消逾期天数配置
     */
    @Value("${hxbk.overDueDayAdd}")
    private Integer hxbkOverDueDayAdd;

    @Value("${mayi.sftp.username}")
    private String sftpUsername;
    @Value("${mayi.sftp.password}")
    private String sftpPassword;
    @Value("${mayi.sftp.host}")
    private String sftpHost;
    @Value("${mayi.sftp.port}")
    private Integer sftpPort;
    @Value("${mayi.sftp.download.loan}")
    private String loanDir;
    @Value("${mayi.sftp.download.repay}")
    private String repayDir;
    @Value("${mayi.sftp.download.contract}")
    private String contractDir;
    @Value("${mayi.sftp.download.idcard}")
    private String idCardDir;
    @Value("${mayi.sftp.download.photo}")
    private String photoDir;
    @Value("${hxbk.oss.bucket}")
    private String HXBKOssBucket;
    @Value("${hxbk.oss.image.expire.seconds}")
    private int ossImageExpireSeconds;

    @Value("${hxbk.loan.fundCode}")
    private String loanFundCode;

    public String getLoanFundCode() {
        return loanFundCode;
    }

    public void setLoanFundCode(String loanFundCode) {
        this.loanFundCode = loanFundCode;
    }

    public String getBaseUrl() {
        return baseUrl;
    }

    public void setBaseUrl(String baseUrl) {
        this.baseUrl = baseUrl;
    }

    public String getAk() {
        return ak;
    }

    public void setAk(String ak) {
        this.ak = ak;
    }

    public String getSk() {
        return sk;
    }

    public void setSk(String sk) {
        this.sk = sk;
    }

    public String getHXBKOssBucket() {
        return HXBKOssBucket;
    }

    public void setHXBKOssBucket( String HXBKOssBucket ) {
        this.HXBKOssBucket = HXBKOssBucket;
    }

    public String getLoanStartTime() {
        return loanStartTime;
    }

    public void setLoanStartTime(String loanStartTime) {
        this.loanStartTime = loanStartTime;
    }

    public String getLoanEndTime() {
        return loanEndTime;
    }

    public void setLoanEndTime(String loanEndTime) {
        this.loanEndTime = loanEndTime;
    }

    public String getPartnerPrivateKey() {
        return partnerPrivateKey;
    }

    public void setPartnerPrivateKey(String partnerPrivateKey) {
        this.partnerPrivateKey = partnerPrivateKey;
    }

    public String getAntPublicKey() {
        return antPublicKey;
    }

    public void setAntPublicKey(String antPublicKey) {
        this.antPublicKey = antPublicKey;
    }

    public String getMerchantShop() {
        return merchantShop;
    }

    public void setMerchantShop(String merchantShop) {
        this.merchantShop = merchantShop;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public Double getCreditRate() {
        return creditRate;
    }

    public void setCreditRate(Double creditRate) {
        this.creditRate = creditRate;
    }

    public String getSftpUsername() {
        return sftpUsername;
    }

    public void setSftpUsername(String sftpUsername) {
        this.sftpUsername = sftpUsername;
    }

    public String getSftpPassword() {
        return sftpPassword;
    }

    public void setSftpPassword(String sftpPassword) {
        this.sftpPassword = sftpPassword;
    }

    public String getSftpHost() {
        return sftpHost;
    }

    public void setSftpHost(String sftpHost) {
        this.sftpHost = sftpHost;
    }

    public Integer getSftpPort() {
        return sftpPort;
    }

    public void setSftpPort(Integer sftpPort) {
        this.sftpPort = sftpPort;
    }

    public String getLoanDir() {
        return loanDir;
    }

    public void setLoanDir(String loanDir) {
        this.loanDir = loanDir;
    }

    public String getRepayDir() {
        return repayDir;
    }

    public void setRepayDir(String repayDir) {
        this.repayDir = repayDir;
    }

    public String getContractDir() {
        return contractDir;
    }

    public void setContractDir(String contractDir) {
        this.contractDir = contractDir;
    }

    public String getIdCardDir() {
        return idCardDir;
    }

    public void setIdCardDir(String idCardDir) {
        this.idCardDir = idCardDir;
    }

    public String getPhotoDir() {
        return photoDir;
    }

    public void setPhotoDir(String photoDir) {
        this.photoDir = photoDir;
    }

    public String getYearPeriodStartTime() {
        return yearPeriodStartTime;
    }

    public void setYearPeriodStartTime(String yearPeriodStartTime) {
        this.yearPeriodStartTime = yearPeriodStartTime;
    }

    public String getYearPeriodEndTime() {
        return yearPeriodEndTime;
    }

    public void setYearPeriodEndTime(String yearPeriodEndTime) {
        this.yearPeriodEndTime = yearPeriodEndTime;
    }

    public String getRepayDarkPeriodStartTime() {
        return repayDarkPeriodStartTime;
    }

    public void setRepayDarkPeriodStartTime(String repayDarkPeriodStartTime) {
        this.repayDarkPeriodStartTime = repayDarkPeriodStartTime;
    }

    public String getRepayDarkPeriodEndTime() {
        return repayDarkPeriodEndTime;
    }

    public void setRepayDarkPeriodEndTime(String repayDarkPeriodEndTime) {
        this.repayDarkPeriodEndTime = repayDarkPeriodEndTime;
    }

    public String getRepayTimeoutConf() {
        return repayTimeoutConf;
    }


    public void setRepayTimeoutConf(String repayTimeoutConf) {
        this.repayTimeoutConf = repayTimeoutConf;
    }

    public int getOssImageExpireSeconds() {
        return ossImageExpireSeconds;
    }

    public void setOssImageExpireSeconds(int ossImageExpireSeconds) {
        this.ossImageExpireSeconds = ossImageExpireSeconds;
    }

    public Integer getHxbkOverDueDayAdd() {
        return hxbkOverDueDayAdd;
    }

    public void setHxbkOverDueDayAdd(Integer hxbkOverDueDayAdd) {
        this.hxbkOverDueDayAdd = hxbkOverDueDayAdd;
    }
}
