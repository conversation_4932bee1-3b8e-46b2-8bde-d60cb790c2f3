package com.jinghang.capital.core.banks.cybk.recc;


import com.jinghang.capital.core.banks.cybk.enums.CYBKLoanPurpose;
import com.jinghang.capital.core.entity.Account;
import com.jinghang.capital.core.entity.CYBKCreditFlow;
import com.jinghang.capital.core.entity.Credit;
import com.jinghang.capital.core.entity.Loan;
import com.jinghang.capital.core.entity.LoanReplan;
import com.jinghang.capital.core.service.WarningService;
import com.jinghang.capital.core.vo.recc.ReccType;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/28 15:37
 * <p>
 * 长银直连客户真实借款
 * <p>
 */
@Component
public class CYBKCustLoanReccHandler extends CYBKReccAbstractHandler {

    private static final Logger logger = LoggerFactory.getLogger(CYBKCustLoanReccHandler.class);
    private static final int TWO = 2;
    private static final int SIX = 6;

    @Autowired
    private WarningService warningService;

    @Override
    public void process(LocalDate reccDay) {

        // 查询昨天的所有放款
        List<String> reccLoans = findCustReccLoans(reccDay);

        if (CollectionUtils.isEmpty(reccLoans)) {
            logger.info("日期" + reccDay.toString() + "无放款订单");
        }

        File sourceFile = null;
        File tempDir = null;
        File okFile = null;
        try {

            Path path = Files.createTempDirectory("CYBK");
            tempDir = path.toFile();

            String filePre = DateFormatUtils.format(new Date(), "yyyyMMdd") + "对客放款明细文件";
            sourceFile = File.createTempFile(tempDir.getAbsolutePath() + filePre, ".csv");
            CSVPrinter printer = CSVFormat.DEFAULT.withSkipHeaderRecord()
                .withDelimiter(',').print(sourceFile, StandardCharsets.UTF_8);

            //表头
            // 合作机构贷款唯一编号,长银授信流水号,合作机构授信流水号,产品编号,长银客户号,合作机构客户号,客户真实姓名,证件类型,客户证件号码,借款人联系方式,借款人地址,贷款状态,贷款用途,
            // 贷款用途描述,贷款资金使用位置,申请支用日期,申请支用时间",放款日期,放款时间,币种,放款金额,贷款起息日,贷款到期日,贷款期次数,还款方式",宽限期天数,利率类型,贷款年利率,
            // 基准年天数,罚息年利率,罚息利率基准年天数,罚息日利率,罚息计算基数,本金还款频率",利息还款频率,担保类型,授信编号,收款帐号类型,收款帐号,还款帐号类型,还款帐号
            List<String> header = List.of(
                "loan_seq", "appl_seq", "out_appl_seq", "loan_typ", "cust_id", "out_cust_id", "name", "id_type", "id_no",
                "mobile", "address", "loan_status", "loan_use", "loan_use_desc", "use_area", "apply_date", "apply_time",
                "encash_date", "encash_time", "ccy", "encash_amt", "start_date", "end_date", "total_terms", "repay_mode",
                "grace_day", "rate_type", "int_rate", "base_int_rate_day", "od_int_rate", "base_od_int_rate_day", "od_int_day_rate", "base_od_amt",
                "prin_repay_frequency", "int_repay_frequency", "guarantee_type", "credit_no", "encash_acct_type", "encash_acct_no", "repay_acct_type",
                "repay_acct_no");
            printer.printRecord(header);

            for (String loanId : reccLoans) {
                Loan loan = getLoanRepository().findById(loanId).orElseThrow();
                Account account = getAccountRepository().findById(loan.getAccountId()).orElseThrow();
                CYBKCreditFlow creditFlow = getCybKCreditFlowRepository().findByCreditId(loan.getCreditId()).orElseThrow();
                Credit credit = getCreditRepository().findById(loan.getCreditId()).orElseThrow();
                CYBKLoanPurpose loanPurpose = CYBKLoanPurpose.getEnumByLoanPurpose(credit.getLoanPurpose());
                LoanReplan maxLoanReplan = getLoanReplanRepository().findByLoanIdAndPeriod(loan.getId(), loan.getPeriods()).orElseThrow();
                List<String> custLoanDTOList = new ArrayList<>();
                custLoanDTOList.add(loan.getId());
                custLoanDTOList.add(credit.getCreditNo());
                custLoanDTOList.add(loan.getCreditId());
                custLoanDTOList.add(getCybkConfig().getLoanType(null));
                custLoanDTOList.add(creditFlow.getCustId());
                custLoanDTOList.add(loan.getAccountId());
                custLoanDTOList.add(account.getName());
                custLoanDTOList.add("20"); //身份证
                custLoanDTOList.add(account.getCertNo());
                custLoanDTOList.add(account.getMobile());
                custLoanDTOList.add(account.getCertAddress());
                custLoanDTOList.add("1"); //放款成功
                custLoanDTOList.add(loanPurpose.getCode());
                custLoanDTOList.add(loanPurpose.getDesc());
                custLoanDTOList.add("1");
                custLoanDTOList.add(loan.getCreatedTime().format(DateTimeFormatter.ISO_LOCAL_DATE));
                custLoanDTOList.add(loan.getCreatedTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                custLoanDTOList.add(loan.getLoanTime().format(DateTimeFormatter.ISO_LOCAL_DATE));
                custLoanDTOList.add(loan.getLoanTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                custLoanDTOList.add("CNY");
                custLoanDTOList.add(loan.getLoanAmt().movePointRight(TWO).toPlainString());
                custLoanDTOList.add(loan.getLoanTime().format(DateTimeFormatter.ISO_LOCAL_DATE));
                custLoanDTOList.add(maxLoanReplan.getRepayDate().format(DateTimeFormatter.ISO_LOCAL_DATE));
                custLoanDTOList.add(loan.getPeriods().toString());
                custLoanDTOList.add("1");
                custLoanDTOList.add("0");
                custLoanDTOList.add("F");
                custLoanDTOList.add(loan.getCustomRate().toPlainString());
                custLoanDTOList.add("360");
                custLoanDTOList.add(new BigDecimal("0.000650").multiply(new BigDecimal("360")).toPlainString());
                custLoanDTOList.add("360");
                //罚息日利率
                custLoanDTOList.add("0.000650");
                custLoanDTOList.add("01");
                custLoanDTOList.add("03");
                custLoanDTOList.add("03");
                custLoanDTOList.add("B");
                custLoanDTOList.add(credit.getId());
                custLoanDTOList.add("");
                custLoanDTOList.add("");
                custLoanDTOList.add("");
                custLoanDTOList.add("");

                printer.printRecord(custLoanDTOList);
            }

            printer.close();

            String uploadPath = getCustReccFilePath(reccDay);

            String dateStr = reccDay.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            // /download/cyxf/{产品编码}/out/files/{YYYYMMDD}/loan_${yyyymmdd}.csv
            String fileName = "loan_" + dateStr + ".csv";
            String okFileName = "loan_" + dateStr + ".csv.ok";
            // 上传oss
            logger.info("长银直连上传对客放款文件");
            getCybkSftpService().upload(uploadPath + fileName, sourceFile.getAbsolutePath().toString());

            // 生成 ok 文件
            Path localVerifyFilePath = Files.createTempFile("loan_" + dateStr, ".csv.ok");
            OutputStream verifyOs = Files.newOutputStream(localVerifyFilePath);
            ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(String.valueOf(reccLoans.size()).getBytes(StandardCharsets.UTF_8));
            IOUtils.copy(byteArrayInputStream, verifyOs);
            getCybkSftpService().upload(uploadPath + okFileName, localVerifyFilePath.toAbsolutePath().toString());
            okFile = localVerifyFilePath.toFile();
        } catch (Exception e) {
            logger.error("长银直连上传对客放款文件异常", e);
            warningService.warn("长银直连上传对客放款文件异常");
        } finally {
            if (sourceFile != null) {
                sourceFile.delete();
            }
            if (okFile != null) {
                okFile.delete();
            }
            if (tempDir != null) {
                tempDir.delete();
            }
        }
    }

    @Override
    public ReccType getReccType() {
        return ReccType.CUST_LOAN;
    }
}
