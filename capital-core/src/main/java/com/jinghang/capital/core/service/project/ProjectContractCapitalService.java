package com.jinghang.capital.core.service.project;

import com.jinghang.capital.core.entity.project.ProjectContractCapital;
import com.jinghang.capital.core.enums.FileType;
import com.jinghang.capital.core.enums.LoanStage;
import com.jinghang.capital.core.repository.project.ProjectContractCapitalRepository;
import com.jinghang.ppd.api.enums.AbleStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/19 14:46
 */
@Service
public class ProjectContractCapitalService {

    @Autowired
    private ProjectContractCapitalRepository projectContractCapitalRepository;


    /**
     * 获取项目合同配置
     * @param projectCode
     * @return
     */
    public List<ProjectContractCapital> getProjectContractCapitals(String projectCode) {
        return projectContractCapitalRepository.findProjectContractFlowsByProjectCodeAndEnabled(projectCode, AbleStatus.ENABLE);
    }

    /**
     * 获取项目合同配置
     * @param projectCode
     * @return
     */
    public List<ProjectContractCapital> getProjectContractCapitalsByLoanState(String projectCode, LoanStage loanStage) {
        List<ProjectContractCapital> projectContractFlowsByProjectCodeAndLoanStageAndEnabled = projectContractCapitalRepository.findProjectContractFlowsByProjectCodeAndLoanStageAndEnabled(projectCode, loanStage, AbleStatus.ENABLE);
        if (projectContractFlowsByProjectCodeAndLoanStageAndEnabled.isEmpty()) {
            return new ArrayList<>();
        }
        return projectContractFlowsByProjectCodeAndLoanStageAndEnabled;
    }


    /**
     * 根据协议编号获取项目合同配置
     * @param agreementCode
     * @return
     */
    public ProjectContractCapital getProjectContractCapital(String agreementCode) {
        return  projectContractCapitalRepository.findProjectContractFlowsByAgreementCodeAndEnabled(agreementCode, AbleStatus.ENABLE);
    }

    /**
     * 根据项目编号和文件类型获取项目合同配置
     * @param projectCode
     * @param fileType
     * @return
     */
    public ProjectContractCapital getCapitalByProjectCodeAndFileType(String projectCode, FileType fileType,LoanStage loanStage) {
        return projectContractCapitalRepository.findProjectContractCapitalByProjectCodeAndFileTypeAndLoanStageAndEnabled(projectCode, fileType,loanStage, AbleStatus.ENABLE);
    }
}
