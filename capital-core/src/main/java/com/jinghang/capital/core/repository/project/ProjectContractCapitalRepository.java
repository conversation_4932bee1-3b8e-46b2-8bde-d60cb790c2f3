package com.jinghang.capital.core.repository.project;

import com.jinghang.capital.core.entity.project.ProjectContractCapital;
import com.jinghang.capital.core.enums.FileType;
import com.jinghang.capital.core.enums.LoanStage;
import com.jinghang.ppd.api.enums.AbleStatus;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

/**
 * 项目资金合同Repository
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/19 13:58
 */
public interface ProjectContractCapitalRepository extends JpaRepository<ProjectContractCapital, Long> {

    /**
     * 根据项目编码查询项目资金合同
     *
     * @param projectCode 项目编码
     * @return 项目资金合同
     */
    ProjectContractCapital findByProjectCode(String projectCode);

    /**
     * 根据项目编码和状态查询项目资产合同列表
     *
     * @param projectCode 项目编码
     * @param enabled 合同状态
     * @return 项目资产合同列表
     */
    List<ProjectContractCapital> findProjectContractFlowsByProjectCodeAndEnabled(String projectCode, AbleStatus enabled);

    /**
     * 根据合同模板编码查询项目资产合同
     * @param agreementCode
     * @param ableStatus
     * @return
     */
    ProjectContractCapital findProjectContractFlowsByAgreementCodeAndEnabled(String agreementCode, AbleStatus ableStatus);

    /**
     * 根据项目编码和状态查询项目资产合同列表
     *
     * @param projectCode 项目编码
     * @param enabled 合同状态
     * @param loanStage 签署阶段
     * @return 项目资产合同列表
     */
    List<ProjectContractCapital> findProjectContractFlowsByProjectCodeAndLoanStageAndEnabled(String projectCode, LoanStage loanStage, AbleStatus enabled);


    /**
     * 根据项目编码、文件类型、签署阶段和状态查询项目资金合同
     *
     * @param projectCode 项目编码
     * @param fileType 文件类型
     * @param loanStage 签署阶段
     * @param ableStatus 合同状态
     * @return 项目资金合同
     */
    ProjectContractCapital findProjectContractCapitalByProjectCodeAndFileTypeAndLoanStageAndEnabled(String projectCode, FileType fileType,LoanStage loanStage, AbleStatus ableStatus);
}
