package com.jinghang.capital.core.banks.cybk.enums;


import com.jinghang.capital.core.enums.Relation;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2023/8/27
 */
public enum CYBKContactType {
    ONE("01", "父母", Relation.PARENTS),
    TWO("02", "配偶", Relation.SPOUSE),
    THREE("03", "亲属", Relation.RELATIVE),
    THREE_2("03", "亲属", Relation.SIBLING),
    FOUR("04", "子女", Relation.CHILDREN),
    FIVE("05", "朋友", Relation.FRIEND),
    SIX("06", "师生", null),
    SEVEN("07", "同学", null),
    EIGHT("08", "同事", Relation.COLLEAGUE),
    NINE("09", "开拓人", null),
    TEN("10", "保证人", null),
    ELEVEN("11", "客户经理", null),
    TWELVE("99", "其他", Relation.UNKNOWN);
    private final String code;
    private final String desc;
    private final Relation relation;

    CYBKContactType(String code, String desc, Relation relation) {
        this.code = code;
        this.desc = desc;
        this.relation = relation;
    }

    public static String getCodeByRelation(Relation relation) {
        return Arrays.stream(values()).filter(l -> relation.equals(l.relation)).findFirst().orElseThrow().code;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public Relation getRelation() {
        return relation;
    }
}
