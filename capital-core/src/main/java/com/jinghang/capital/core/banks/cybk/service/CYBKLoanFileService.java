package com.jinghang.capital.core.banks.cybk.service;

import com.jinghang.capital.api.dto.GuaranteeCompany;
import com.jinghang.capital.api.dto.repay.RepayReturnUploadResultDto;
import com.jinghang.capital.core.banks.AbstractBankFileService;
import com.jinghang.capital.core.banks.cybk.config.CYBKConfig;
import com.jinghang.capital.core.banks.cybk.config.CYBKSftpConfig;
import com.jinghang.capital.core.banks.cybk.dto.credit.CYBKFileInfo;
import com.jinghang.capital.core.banks.cybk.dto.file.CYBKCLearVoucherQueryResponse;
import com.jinghang.capital.core.banks.cybk.dto.file.CYBKClearVoucherApplyRequest;
import com.jinghang.capital.core.banks.cybk.dto.file.CYBKClearVoucherApplyResponse;
import com.jinghang.capital.core.banks.cybk.dto.file.CYBKClearVoucherQueryRequest;
import com.jinghang.capital.core.banks.cybk.enums.CYBKFileType;
import com.jinghang.capital.core.banks.cybk.remote.CYBKRequestService;
import com.jinghang.capital.core.entity.CYBKCreditFlow;
import com.jinghang.capital.core.entity.Credit;
import com.jinghang.capital.core.entity.DownloadFileLog;
import com.jinghang.capital.core.entity.Loan;
import com.jinghang.capital.core.entity.LoanFile;
import com.jinghang.capital.core.entity.ReconciliationFile;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.DownloadFileStatusEnum;
import com.jinghang.capital.core.enums.FileType;
import com.jinghang.capital.core.enums.LoanStage;
import com.jinghang.capital.core.enums.ProcessStatus;
import com.jinghang.capital.core.enums.RepayType;
import com.jinghang.capital.core.exception.BizErrorCode;
import com.jinghang.capital.core.exception.BizException;
import com.jinghang.capital.core.repository.*;
import com.jinghang.capital.core.service.CommonService;
import com.jinghang.capital.core.service.FileService;
import com.jinghang.capital.core.service.WarningService;
import com.jinghang.capital.core.service.credit.FinCreditService;
import com.jinghang.capital.core.vo.ProductVo;
import com.jinghang.capital.core.vo.file.FileDailyProcessVo;
import com.jinghang.capital.core.vo.file.FileDownloadRequestVo;
import com.jinghang.capital.core.vo.file.FileDownloadResultVo;
import com.jinghang.capital.core.vo.file.FileDownloadVo;
import com.jinghang.capital.core.vo.file.FileUploadResultVo;
import com.jinghang.capital.core.vo.file.FileUploadVo;
import com.jinghang.capital.core.vo.file.PreviewResultVo;
import com.jinghang.capital.core.vo.file.PreviewVo;


import com.jinghang.capital.core.vo.repay.RepayReturnUploadVo;
import com.jinghang.common.sftp.DestMapping;
import com.jinghang.common.sftp.Sftp;
import com.jinghang.common.sftp.exception.SftpException;
import com.jinghang.common.util.JsonUtil;
import com.jinghang.common.util.SftpUtil;
import com.jinghang.common.util.StringUtil;
import jakarta.annotation.PostConstruct;
import org.redisson.api.RRateLimiter;
import org.redisson.api.RateIntervalUnit;
import org.redisson.api.RateType;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2023/8/20
 */
@Service
public class CYBKLoanFileService extends AbstractBankFileService {

    public static final String SLASH = "/";
    public static final String UNDERSCORE = "_";
    public static final String DOT = ".";
    public static final int JOB_BATCH_DAYS = 7;
    public static final int JOB_SIZE = 10;

    private static final long THIRTY_REQUEST = 30L;

    private static final long ONE_SECOND = 1L;
    private static final Logger logger = LoggerFactory.getLogger(CYBKLoanFileService.class);

    private static final String CREDIT_SETTLE_VOUCHER_FILE_DOWNLOAD = "cybk:credit_settle_voucher_file_download";
    private static final String CLEAR_VOUCHER_SUCCESS_CODE = "0";
    private static final String CLEAR_VOUCHER_FAIL_CODE = "1";
    private static final String CLEAR_VOUCHER_PROCESS_CODE = "2";

    private Map<String, RRateLimiter> rateLimiters = new ConcurrentHashMap<>();
    @Autowired
    private CommonService commonService;

    @Autowired
    private WarningService warningService;
    @Autowired
    private CYBKConfig config;
    @Autowired
    private CYBKSftpConfig sftpConfig;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private CYBKRequestService requestService;
    @Autowired
    private FileService fileService;
    @Autowired
    private DownloadFileLogRepository downloadFileLogRepository;
    @Autowired
    private BankLoanReplanRepository bankLoanReplanRepository;
    @Autowired
    private CYBKCreditFlowRepository cybkCreditFlowRepository;
    @Autowired
    private LoanRepository loanRepository;

    @Override
    public FileUploadResultVo upload(FileUploadVo uploadVo) {
        return null;
    }

    @Override
    public FileDownloadResultVo download(FileDownloadVo downloadVo) {
        FileType fileType = downloadVo.getType();

        return switch (fileType) {
            case LOAN_CONTRACT, ENTRUSTED_GUARANTEE_CONTRACT -> downloadLoanContract(downloadVo);
            case REPAYMENT_FILE, LOAN_FILE -> downloadReccFile(downloadVo);
            case CREDIT_SETTLE_VOUCHER_FILE -> downloadCreditSettleVoucher(downloadVo);
            default -> downloadForOutOrderId(downloadVo);
        };
    }

    private FileDownloadResultVo downloadLoanContract(FileDownloadVo downloadVo) {
        Loan loan = commonService.findLoanById(downloadVo.getLoanId());
        FileDownloadResultVo resultVo = new FileDownloadResultVo();
        try {
            LoanFile loanFile = downloadLoanContract(loan, downloadVo.getType());
            resultVo.setOssBucket(loanFile.getOssBucket());
            resultVo.setOssPath(loanFile.getOssKey());
            resultVo.setFileName(loanFile.getFileName());
            resultVo.setFileStatus(loanFile.getSignStatus());
            String ossUrl = fileService.getOssUrl(loanFile.getOssBucket(), loanFile.getOssKey());
            resultVo.setFileUrl(ossUrl);
        } catch (BizException e) {
            resultVo.setFileStatus(ProcessStatus.FAIL);
        } catch (IOException e) {
            logger.warn("下载长银直连合同异常, loanId:{}, fileType:{}", loan.getId(), downloadVo.getType(), e);
            resultVo.setFileStatus(ProcessStatus.FAIL);
        }

        return resultVo;
    }

    /**
     * 下载借款合同、 委托担保合同
     *
     * @param loan
     * @throws IOException
     */
    public LoanFile downloadLoanContract(Loan loan, FileType fileType) throws IOException {
        logger.info("下载长银直连最终签署的合同, loanId: {},fileType:{}", loan.getId(), fileType);

        GuaranteeCompany guaranteeCompany = loan.getGuaranteeCompany();
        List<LoanFile> loanFiles = commonService.getLoanFileRepository().findByRelatedIdAndFileType(loan.getId(), fileType);
        if (!CollectionUtils.isEmpty(loanFiles)) {
            LoanFile loanFile = loanFiles.get(0);
            if (loanFile.getSignStatus() == ProcessStatus.SUCCESS) {
                logger.warn("合同已下载, loanId: {}, fileType:{}", loan.getId(), fileType);
                return loanFile;
            }
        }

        Credit credit = commonService.getCreditRepository().findById(loan.getCreditId()).orElseThrow();

        CYBKFileType cybkFileType = CYBKFileType.getEnumByFileType(fileType);

        CYBKFileInfo cybkFileInfo = new CYBKFileInfo();
        cybkFileInfo.setApplCde(loan.getLoanNo());
        //cybkFileInfo.setBusinessStage("2"); //放款阶段
        cybkFileInfo.setImgType(cybkFileType.getCode());
        cybkFileInfo.setIdNo(credit.getCustCertNo());

        String loanTimeStr = loan.getLoanTime().format(DateTimeFormatter.BASIC_ISO_DATE);
        String fileBase64;
        try {
            fileBase64 = requestService.downloadImage(cybkFileInfo,credit.getGuaranteeCompany());
        } catch (Exception e) {
            logger.error("从长银直连下载合同出错", e);
            throw e;
        }
        String ossFilePath = "cybk/contract/" + loanTimeStr + "/" + loan.getId() + "_" + loan.getLoanNo() + "_" + fileType + ".pdf";

        try {
            fileService.uploadOss(config.getCybkOssBucket(guaranteeCompany), ossFilePath, fileBase64);
            LoanFile loanFile = new LoanFile();
            loanFile.setCreditId(loan.getCreditId());
            loanFile.setRelatedId(loan.getId());
            loanFile.setStage(LoanStage.LOAN.name());
            loanFile.setOssBucket(config.getCybkOssBucket(guaranteeCompany));
            loanFile.setOssKey(ossFilePath);
            loanFile.setChannel(BankChannel.CYBK);
            loanFile.setFileType(fileType);
            loanFile.setFileName(fileType.getDesc());
            loanFile.setSignStatus(ProcessStatus.SUCCESS);
            return commonService.getLoanFileRepository().save(loanFile);
        } catch (Exception e) {
            logger.error("上传借款合同到OSS异常, loanId: {}, ossBucket:{}, ossKey: {}", loan.getId(), config.getCybkOssBucket(guaranteeCompany), ossFilePath, e);
            throw new BizException(BizErrorCode.FILE_UPLOAD_ERROR.getCode(), "长银直连合同下载失败，Oss上传出错");
        }
    }

    @Override
    public void processDaily(FileDailyProcessVo processVo) {
        LocalDate processDate = processVo.getProcessDate();
        if (processDate == null) {
            processDate = LocalDate.now();
        }
        FileType type = processVo.getType();
        final LocalDate workDate = processDate;

        switch (type) {
            // 借款合同、委托担保合同
            case LOAN_CONTRACT -> processLoanContract(workDate);
            // 结清证明申请  查询结果并下载
            case CREDIT_SETTLE_VOUCHER_FILE -> processCreditSettleVoucherQuery();
            default -> {
            }
        }
    }

    @Override
    public void downloadRequest(FileDownloadRequestVo requestVo) {

    }

    @Override
    public PreviewResultVo preview(PreviewVo previewVo) {
        return null;
    }

    @Override
    public RepayReturnUploadResultDto offlineRepayReturnFileUpload(RepayReturnUploadVo applyVo) {
        return null;
    }


    @Override
    public void batchVoucherDownload(FileDailyProcessVo processVo) {
        LocalDate processDate = processVo.getProcessDate();
        if (processDate == null) {
            processDate = LocalDate.now().minusDays(1);
        }
        FileType type = processVo.getType();
        final LocalDate workDate = processDate;

        switch (type) {
            //结清证明申请
            case CREDIT_SETTLE_VOUCHER_FILE -> downloadCreditSettleVoucher(workDate);
            default -> logger.error("unknown file type={" + type + "}");
        }
    }


    @Async("fileProcessThreadPool")
    public void downloadCreditSettleVoucher(LocalDate workDate) {
        logger.warn("异步查询结清申请: [{}] 开始", workDate.toString());
        LocalDateTime clearStartOfDay = workDate.atStartOfDay();
        LocalDateTime clearNextDayStart = workDate.plusDays(1L).atStartOfDay();
        // 正常按计划结清的
        List<String> normalClear = bankLoanReplanRepository.findNormalClear(BankChannel.CYBK, RepayType.REPAY, clearStartOfDay, clearNextDayStart);
        // 提前结清的
        List<String> advancedClear = bankLoanReplanRepository.findAdvancedClear(BankChannel.CYBK, RepayType.REPAY, clearStartOfDay, clearNextDayStart);
        List<String> result = new ArrayList<>();
        result.addAll(normalClear);
        result.addAll(advancedClear);

        for (String loanId : result) {
            //先查数据库是否已经下载过了
            LoanFile loanFile = commonService.getLoanFileRepository()
                .findFirstByRelatedIdAndFileTypeAndChannelOrderByCreatedTimeDesc(loanId, FileType.CREDIT_SETTLE_VOUCHER_FILE, BankChannel.CYBK);
            if (Objects.nonNull(loanFile)) {
                logger.warn("贷款编号: [{}] ，长银直连 结清证明文件已经生成，本次跳过。", loanId);
                continue;
            }
            downloadCreditSettleVoucherApply(loanId);
        }
        logger.warn("异步查询结清申请: [{}] 结束", workDate.toString());
    }

    private FileDownloadResultVo downloadCreditSettleVoucher(FileDownloadVo downloadVo) {
        FileDownloadResultVo resultVo = new FileDownloadResultVo();

        // 先查数据库是否已经下载过了
        LoanFile loanFile = commonService.getLoanFileRepository()
            .findFirstByRelatedIdAndFileTypeAndChannelOrderByCreatedTimeDesc(downloadVo.getLoanId(), downloadVo.getType(), BankChannel.CYBK);

        if (Objects.nonNull(loanFile)) {
            return processFileResult(loanFile);
        } else {
            try {
                DownloadFileLog downloadFileLog = downloadFileLogRepository.findFirstByBizIdAndFileTypeOrderByCreateTimeDesc(
                    downloadVo.getLoanId(), FileType.CREDIT_SETTLE_VOUCHER_FILE);
                if (Objects.isNull(downloadFileLog) || DownloadFileStatusEnum.F == downloadFileLog.getStatus()) {
                    logger.info("长银直连，结清证明下载，调用外部接口申请证明，loanId:{}", downloadVo.getLoanId());
                    //申请开具结清证明
                    downloadCreditSettleVoucherApply(downloadVo.getLoanId());
                } else if (DownloadFileStatusEnum.P == downloadFileLog.getStatus()) {
                    //查询开具结果并下载文件
                    downloadCreditSettleVoucherQuery(downloadFileLog);

                    //重新查询已下载文件记录 并返回下载链接
                    loanFile = commonService.getLoanFileRepository()
                        .findFirstByRelatedIdAndFileTypeAndChannelOrderByCreatedTimeDesc(downloadVo.getLoanId(), downloadVo.getType(), BankChannel.CYBK);
                    if (Objects.nonNull(loanFile)) {
                        return processFileResult(loanFile);
                    }
                    resultVo.setFileStatus(ProcessStatus.PROCESSING);
                }
            } catch (Exception e) {
                logger.error("长银直连结清证明下载失败", e);
            }
        }
        resultVo.setFileStatus(ProcessStatus.PROCESSING);
        return resultVo;
    }


    private void downloadCreditSettleVoucherApply(String loanId) {
        DownloadFileLog downLog = downloadFileLogRepository.findFirstByBizIdAndFileTypeOrderByCreateTimeDesc(loanId, FileType.CREDIT_SETTLE_VOUCHER_FILE);
        if (Objects.isNull(downLog) || DownloadFileStatusEnum.F == downLog.getStatus()) {
            logger.info("长银直连开具结清证明 申请，loanId:{}", loanId);
            DownloadFileLog downloadTaskLog = new DownloadFileLog();
            downloadTaskLog.setBizId(loanId);
            downloadTaskLog.setBankChannel(BankChannel.CYBK);
            downloadTaskLog.setFileType(FileType.CREDIT_SETTLE_VOUCHER_FILE);
            downloadTaskLog.setCreateTime(LocalDateTime.now());
            downloadTaskLog.setStatus(DownloadFileStatusEnum.I);
            downloadFileLogRepository.save(downloadTaskLog);
            //每秒调用三十次接口
            getRateLimiter(CREDIT_SETTLE_VOUCHER_FILE_DOWNLOAD).acquire();
            downloadSettle(loanId, downloadTaskLog);
        }
    }

    private void downloadSettle(String loanId, DownloadFileLog downloadTaskLog) {
        logger.info("下载结清文件, 请求贷款编号: [{}] ，开始长银直连证明申请", loanId);
        try {
            Loan loan = commonService.findLoanById(loanId);
            sendVoucherApplyRequest(loan, downloadTaskLog);
        } catch (Exception e) {
            logger.error("下载结清文件, 贷款编号: [{}] 开始长银直连证明申请失败，异常信息: ", loanId, e);
            downloadTaskLog.setUpdateTime(LocalDateTime.now());
            downloadTaskLog.setStatus(DownloadFileStatusEnum.F);
            downloadTaskLog.setRemark(e.getMessage());
            downloadFileLogRepository.save(downloadTaskLog);
            throw e;
        }
    }


    @Async("fileProcessThreadPool")
    public void sendVoucherApplyRequest(Loan loan, DownloadFileLog downloadFileLog) {
        logger.info("长银直连证明文件异步申请,loanId: [{}]", loan.getId());
        try {
            CYBKCreditFlow creditFlow = cybkCreditFlowRepository.findByCreditId(loan.getCreditId()).orElseThrow();

            CYBKClearVoucherApplyRequest request = new CYBKClearVoucherApplyRequest();
            request.setOutSeq(loan.getCreditId());
            request.setLoanNo(creditFlow.getLoanNo());
            request.setBizMode("1"); //1：非额度类
            request.setIdNo(loan.getCustCertNo());

            logger.info("长银直连证明文件申请下载 请求: [{}]", JsonUtil.convertToString(request));
            // 请求长银直连
            CYBKClearVoucherApplyResponse resp = requestService.clearVoucherApply(request,loan.getGuaranteeCompany());
            logger.info("长银直连证明文件申请下载 响应: [{}]", JsonUtil.convertToString(resp));
            if (CLEAR_VOUCHER_FAIL_CODE.equals(resp.getStatus())) {
                logger.error("长银直连请求证明文件申请 处理失败，错误信息为 [{}]", resp.getStatusDesc());
                downloadFileLog.setUpdateTime(LocalDateTime.now());
                downloadFileLog.setStatus(DownloadFileStatusEnum.F);
                downloadFileLog.setRemark(resp.getStatusDesc());
                downloadFileLogRepository.save(downloadFileLog);
            } else {
                downloadFileLog.setUpdateTime(LocalDateTime.now());
                downloadFileLog.setStatus(DownloadFileStatusEnum.P);
                downloadFileLogRepository.save(downloadFileLog);
                logger.info("长银直连证明文件申请成功，后续将通过Job下载文件，loanId: [{}]", loan.getId());
            }
        } catch (Exception e) {
            logger.warn("长银直连证明文件申请失败, 异常: ", e);
            downloadFileLog.setUpdateTime(LocalDateTime.now());
            downloadFileLog.setStatus(DownloadFileStatusEnum.F);
            downloadFileLog.setRemark(e.getMessage());
            downloadFileLogRepository.save(downloadFileLog);
        }
    }

    @Async("fileProcessThreadPool")
    public void processCreditSettleVoucherQuery() {
        logger.info("长银直连结清文件查询 异步申请 开始。loanId: [{}]");
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startTime = now.minusDays(JOB_BATCH_DAYS);
        //7天内
        List<DownloadFileLog> logList = downloadFileLogRepository.findByBankChannelAndStatusAndCreateTime(
            BankChannel.CYBK, DownloadFileStatusEnum.P, startTime);
        for (DownloadFileLog log : logList) {
            downloadCreditSettleVoucherQuery(log);
        }
        logger.info("长银直连结清文件查询 异步申请 结束。loanId: [{}]");
    }

    /**
     * 下载结清证明
     *
     * @param downloadFileLog
     */
    public void downloadCreditSettleVoucherQuery(DownloadFileLog downloadFileLog) {
        String loanId = downloadFileLog.getBizId();
        try {
            Loan loan = commonService.findLoanById(loanId);
            CYBKCreditFlow creditFlow = cybkCreditFlowRepository.findByCreditId(loan.getCreditId()).orElseThrow();

            CYBKClearVoucherQueryRequest request = new CYBKClearVoucherQueryRequest();
            request.setOutSeq(loan.getCreditId());
            request.setLoanNo(creditFlow.getLoanNo());
            request.setBizMode("1"); //1：非额度类
            request.setIdNo(loan.getCustCertNo());

            logger.info("长银直连结清证明文件,查询下载 请求: [{}]", JsonUtil.convertToString(request));
            // 请求长银直连
            CYBKCLearVoucherQueryResponse resp = requestService.clearVoucherQuery(request,loan.getGuaranteeCompany());
            logger.info("长银直连结清证明文件,查询下载 响应: [{}]", JsonUtil.convertToString(resp));
            switch (resp.getStatus()) {
                case CLEAR_VOUCHER_SUCCESS_CODE -> getOfSftp(downloadFileLog, loan, resp.getImgUrl());
                case CLEAR_VOUCHER_FAIL_CODE -> updateDownloadLogFail(downloadFileLog, resp.getStatusDesc());
                case CLEAR_VOUCHER_PROCESS_CODE -> logger.info("长银直连结清证明申请处理中，20分钟后再试，本次结束，loanId: [{}]", loan.getId());
                default -> logger.error("长银直连结清证明,结果查询失败，loanId:[{}]，resp：[{}]", loanId, JsonUtil.convertToString(resp));
            }
        } catch (Exception e) {
            logger.error("长银直连结清证明申请结果查询失败，loanId: [{}]", loanId, e);
        }
    }

    private void getOfSftp(DownloadFileLog downloadFileLog, Loan loan, String sftPath) throws IOException {
        if (StringUtil.isNotBlank(sftPath)) {
            // 1. 从SFTP下载凭证
            Path tempFile = createTempFile();
            Sftp sftp = getSftp();
            try {
                // 下载
                sftp.download(DestMapping.of(sftPath, tempFile.toAbsolutePath().toString()));
            } catch (SftpException e) {
                logger.error("download file from cycfc sftp error", e);
                throw new BizException(BizErrorCode.HTTP_REQUEST_ERROR);
            }
            // 2. 保存文件
            saveLoanFile(downloadFileLog, loan.getCreditId(), FileType.CREDIT_SETTLE_VOUCHER_FILE, sftPath, tempFile);

            deleteTempFile(tempFile);
        }
    }

    private void saveLoanFile(DownloadFileLog downloadFileLog, String creditId, FileType fileType, String sftpFilePath, Path tempFile) throws IOException {
        String loanId = downloadFileLog.getBizId();
        // 获取文件后缀名
        String suffixName = sftpFilePath.substring(sftpFilePath.lastIndexOf(DOT) + 1);
        String dateTimeStr = LocalDateTime.now().format(DateTimeFormatter.BASIC_ISO_DATE);
        String ossFilePath = "cybk/voucher/" + dateTimeStr + SLASH + loanId + UNDERSCORE + "clear" + DOT + suffixName;

        try (InputStream is = Files.newInputStream(tempFile)) {
            Loan loan = loanRepository.findById(loanId).orElseThrow();
            fileService.uploadOss(config.getCybkOssBucket(loan.getGuaranteeCompany()), ossFilePath, is);
            String stage = getStage(fileType);
            LoanFile voucherFile = new LoanFile();
            voucherFile.setRelatedId(loanId);
            voucherFile.setCreditId(creditId);
            voucherFile.setChannel(BankChannel.CYBK);
            voucherFile.setOssBucket(config.getCybkOssBucket(loan.getGuaranteeCompany()));
            voucherFile.setOssKey(ossFilePath);
            voucherFile.setSignStatus(ProcessStatus.SUCCESS);
            voucherFile.setStage(stage);
            voucherFile.setFileType(fileType);
            voucherFile.setFileName(fileType.getDesc());

            LoanFile loanFile = commonService.getLoanFileRepository()
                .findFirstByRelatedIdAndFileTypeAndChannelOrderByCreatedTimeDesc(loanId, fileType, BankChannel.CYBK);

            if (loanFile == null) {
                commonService.getLoanFileRepository().save(voucherFile);
            }

            downloadFileLog.setStatus(DownloadFileStatusEnum.S);
            downloadFileLog.setUpdateTime(LocalDateTime.now());
            downloadFileLogRepository.save(downloadFileLog);
        }

    }

    @Override
    protected String getStage(FileType fileType) {
        return super.getStage(fileType);
    }

    @Override
    public ByteArrayOutputStream getCheckFileStream(String fileName, Integer size) {
        return null;
    }

    private void updateDownloadLogFail(DownloadFileLog downloadFileLog, String remark) {
        logger.error("长银直连结清证明申请结果失败，bizId:{}，失败原因：{}", downloadFileLog.getBizId(), remark);
        downloadFileLog.setUpdateTime(LocalDateTime.now());
        downloadFileLog.setStatus(DownloadFileStatusEnum.F);
        downloadFileLog.setRemark(remark);
        downloadFileLogRepository.save(downloadFileLog);
    }

    private FileDownloadResultVo processFileResult(LoanFile loanFile) {
        FileDownloadResultVo resultVo = new FileDownloadResultVo();
        resultVo.setOssBucket(loanFile.getOssBucket());
        resultVo.setOssPath(loanFile.getOssKey());
        resultVo.setFileName(loanFile.getFileName());
        resultVo.setSignStatus(loanFile.getSignStatus());
        resultVo.setFileStatus(ProcessStatus.SUCCESS);
        String ossUrl = fileService.getOssUrl(loanFile.getOssBucket(), loanFile.getOssKey());
        // 获取文件后缀名
        String suffixName = loanFile.getOssKey().substring(loanFile.getOssKey().lastIndexOf(DOT) + 1);
        resultVo.setFileUrl(ossUrl);
        resultVo.setFileName(loanFile.getFileName() + DOT + suffixName);
        return resultVo;
    }


    private FileDownloadResultVo downloadForOutOrderId(FileDownloadVo downloadVo) {
        FileType fileType = downloadVo.getType();
        Loan loan = commonService.findLoanByOutId(downloadVo.getLoanOrderId());
        // 借据关联的合同
        List<LoanFile> loanFiles = commonService.getLoanFileRepository().findByRelatedIdAndFileType(loan.getId(), fileType);
        if (CollectionUtils.isEmpty(loanFiles)) {
            // 授信关联的合同
            loanFiles = commonService.getLoanFileRepository().findByCreditIdAndFileType(loan.getCreditId(), fileType);
        }

        FileDownloadResultVo resultVo = new FileDownloadResultVo();
        if (!CollectionUtils.isEmpty(loanFiles)) {
            LoanFile loanFile = loanFiles.get(0);
            resultVo.setOssBucket(loanFile.getOssBucket());
            resultVo.setOssPath(loanFile.getOssKey());
            resultVo.setFileName(loanFile.getFileName());
            resultVo.setSignStatus(loanFile.getSignStatus());
            resultVo.setFileStatus(ProcessStatus.SUCCESS);
            String ossUrl = fileService.getOssUrl(loanFile.getOssBucket(), loanFile.getOssKey());
            // 获取文件后缀名
            String suffixName = loanFile.getOssKey().substring(loanFile.getOssKey().lastIndexOf(DOT) + 1);
            resultVo.setFileUrl(ossUrl);
            resultVo.setFileName(loanFile.getFileName() + DOT + suffixName);
        }
        return resultVo;
    }

    /**
     * 业务端下载对账文件
     *
     * @param downloadVo
     * @return
     */
    private FileDownloadResultVo downloadReccFile(FileDownloadVo downloadVo) {
        LocalDate fileDate = downloadVo.getFileDate();
        ReconciliationFile reccFile =
            commonService.getRecFileRepository().findFirstByFileTypeAndProductAndFileDateAndBankChannelOrderByCreatedTimeDesc(downloadVo.getType(),
                ProductVo.ZC_360, fileDate, downloadVo.getBankChannel());

        FileDownloadResultVo resultVo = new FileDownloadResultVo();
        if (reccFile == null) {
            return resultVo;
        }

        resultVo.setOssBucket(reccFile.getTargetOssBucket());
        resultVo.setOssPath(reccFile.getTargetOssKey());
        resultVo.setFileName(reccFile.getFileName());
        return resultVo;
    }


    @Async("fileProcessThreadPool")
    public void processLoanContract(final LocalDate processDate) {
        logger.info("长银直连下载借款合同, processDate:{}", processDate);

        List<Loan> loanList = commonService.findSuccessLoan(BankChannel.CYBK, processDate);

        loanList.forEach(l -> {
//            try {
//                downloadLoanContract(l, FileType.ENTRUSTED_GUARANTEE_CONTRACT);
//            } catch (Exception e) {
//                warningService.warn("下载长银直连, 委托担保合同异常, loanId:" + l.getId(), msg -> logger.error(msg, e));
//            }

            try {
                downloadLoanContract(l, FileType.LOAN_CONTRACT);
            } catch (Exception e) {
                warningService.warn("下载长银直连借款合同异常, loanId:" + l.getId(), msg -> logger.error(msg, e));
            }
        });


    }

    private Sftp getSftp() {
        return SftpUtil.use(sftpConfig.getCybkSftpUsername(), sftpConfig.getCybkSftpPassword(),
            sftpConfig.getCybkSftpHost(), sftpConfig.getCybkSftpPort());
    }

    private Path createTempFile() throws IOException {
        return Files.createTempFile("cycfc", ".tmp");
    }

    private void deleteTempFile(Path tmpFile) {
        if (tmpFile == null) {
            return;
        }
        try {
            Files.delete(tmpFile);
        } catch (IOException e) {
            logger.error("delete cycfc tmp file error, path: {}", tmpFile, e);
        }
    }

    public RRateLimiter getRateLimiter(String name) {
        return rateLimiters.get(name);
    }

    @PostConstruct
    public void initRateLimiter() {
        String[] rateLimiterNames = {CREDIT_SETTLE_VOUCHER_FILE_DOWNLOAD};
        for (String name : rateLimiterNames) {
            RRateLimiter rateLimiter = redissonClient.getRateLimiter(name);
            // 初始化
            rateLimiter.trySetRate(RateType.OVERALL, THIRTY_REQUEST, ONE_SECOND, RateIntervalUnit.SECONDS);
            rateLimiters.put(name, rateLimiter);
        }
    }


    @Override
    public boolean isSupport(BankChannel channel) {
        return BankChannel.CYBK == channel;
    }
}
