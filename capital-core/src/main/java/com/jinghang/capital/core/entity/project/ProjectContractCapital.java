package com.jinghang.capital.core.entity.project;

import com.jinghang.capital.core.enums.FileType;
import com.jinghang.capital.core.enums.LoanStage;
import com.jinghang.ppd.api.enums.AbleStatus;
import jakarta.persistence.*;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 合同模板配置表-资金方
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/19 13:50
 */
@Entity
@Table(name = "project_contract_capital")
public class ProjectContractCapital implements Serializable {

    @Serial
    private static final long serialVersionUID = 1234567890123456739L;

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 项目唯一编码
     */
    @Column(name = "project_code")
    private String projectCode;

    /**
     * 合同模板唯一编码
     */
    @Column(name = "template_code")
    private String templateCode;

    /**
     * 合同英文简称(文件名)
     */
    @Column(name = "contract_file_name")
    private String contractFileName;



    /**
     * 合同模板中文描述
     */
    @Column(name = "contract_description")
    private String contractDescription;

    /**
     * 合同签署阶段
     */
    @Column(name = "loan_stage")
    @Enumerated(EnumType.STRING)
    private LoanStage loanStage;

    /**
     * 模板归属方
     */
    @Column(name = "template_owner")
    private String templateOwner;

    /**
     * 是否融担签章(0=否,1=是)
     */
    @Column(name = "is_rd_signature")
    private String isRdSignature;

    /**
     * 签章类型
     */
    @Column(name = "seal_type")
    private String sealType;

    /**
     * 是否回传资金方(0=否,1=是)
     */
    @Column(name = "is_return_to_capital")
    private String isReturnToCapital;

    /**
     * 模板备注
     */
    private String remark;

    /**
     * 启用状态
     */
    @Enumerated(EnumType.STRING)
    private AbleStatus enabled;

    /**
     * 创建人
     */
    @Column(name = "create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @Column(name = "update_by")
    private String updateBy;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private LocalDateTime updateTime;

    /**
     * 合同协议编码
     */
    @Column(name = "agreement_code")
    private String agreementCode;

    /**
     * 文件类型
     */
    @Enumerated(EnumType.STRING)
    private FileType fileType;

    /**
     * 文件扩展名
     */
    @Column(name = "extension")
    private String extension;

    /**
     * 是否需要重新签署
     */
    @Column(name = "need_sign_again", columnDefinition = "boolean default false")
    private Boolean needSignAgain = false;

    // Getters and Setters
    public Boolean getNeedSignAgain() {
        return needSignAgain;
    }

    public void setNeedSignAgain(Boolean needSignAgain) {
        this.needSignAgain = needSignAgain;
    }

    public String getExtension() {
        return extension;
    }

    public void setExtension(String extension) {
        this.extension = extension;
    }

    public FileType getFileType() {
        return fileType;
    }

    public void setFileType(FileType fileType) {
        this.fileType = fileType;
    }

    public String getAgreementCode() {
        return agreementCode;
    }

    public void setAgreementCode(String agreementCode) {
        this.agreementCode = agreementCode;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public AbleStatus getEnabled() {
        return enabled;
    }

    public void setEnabled(AbleStatus enabled) {
        this.enabled = enabled;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getIsReturnToCapital() {
        return isReturnToCapital;
    }

    public void setIsReturnToCapital(String isReturnToCapital) {
        this.isReturnToCapital = isReturnToCapital;
    }

    public String getSealType() {
        return sealType;
    }

    public void setSealType(String sealType) {
        this.sealType = sealType;
    }

    public String getIsRdSignature() {
        return isRdSignature;
    }

    public void setIsRdSignature(String isRdSignature) {
        this.isRdSignature = isRdSignature;
    }

    public String getTemplateOwner() {
        return templateOwner;
    }

    public void setTemplateOwner(String templateOwner) {
        this.templateOwner = templateOwner;
    }

    public LoanStage getLoanStage() {
        return loanStage;
    }

    public void setLoanStage(LoanStage signStage) {
        this.loanStage = signStage;
    }

    public String getContractDescription() {
        return contractDescription;
    }

    public void setContractDescription(String contractDescription) {
        this.contractDescription = contractDescription;
    }

    public String getContractFileName() {
        return contractFileName;
    }

    public void setContractFileName(String contractFileName) {
        this.contractFileName = contractFileName;
    }

    public String getTemplateCode() {
        return templateCode;
    }

    public void setTemplateCode(String templateCode) {
        this.templateCode = templateCode;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
}