package com.jinghang.capital.core.banks.cybk.config;

import com.jinghang.capital.api.dto.GuaranteeCompany;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
public class CYBKConfig {

    @Value("${cybk.server.url}")
    private String cybkServerUrl;

    @Value("${cybk.appid}")
    private String appid;

    @Value("${cybk.channel}")
    private String channel;

    @Value("${cybk.channel.id}")
    private String channelId;

    @Value("${cybk.merchant.shortCode}")
    private String shortCode;

    @Value("${cybk.loanType}")
    private String loanType;

    @Value("${cybk.merchant.code}")
    private String merchantCode;

    @Value("${cybk.merchant.shop}")
    private String merchantShop;

    @Value("${cybk.merchant.terminal}")
    private String merchantTerminal;

    /**
     * 业务数据 加密密钥
     */
    @Value("${cybk.content.key}")
    private String contentKey;

    /**
     * MD5后的签名 加密密钥
     */
    @Value("${cybk.sign.key}")
    private String signKey;

    @Value("${cybk.oss.bucket}")
    private String cybkOssBucket;

    @Value("${cybk.credit.start}")
    private String creditStartTime;

    @Value("${cybk.credit.end}")
    private String creditEndTime;

    @Value("${cybk.loan.start}")
    private String loanStartTime;

    @Value("${cybk.loan.end}")
    private String loanEndTime;

    @Value("${cybk.repay.start}")
    private String repayStartTime;

    @Value("${cybk.repay.end}")
    private String repayEndTime;

    @Value("${cybk.guarantee.company.address}")
    private String guaranteeCompanyAddress;

    @Value("${cybk.repay.channel}")
    private String repayChannel;

    @Value("${cybk.sendImage.retries}")
    private Integer retries;

    @Value("${cybk.callBack.url}")
    private String callBackUrl;

    @Value("${cybk.hy.server.url}")
    private String hyCybkServerUrl;

    @Value("${cybk.hy.appid}")
    private String hyAppid;

    @Value("${cybk.hy.channel}")
    private String hyChannel;

    @Value("${cybk.hy.channel.id}")
    private String hyChannelId;

    @Value("${cybk.hy.merchant.shortCode}")
    private String hyShortCode;

    @Value("${cybk.hy.loanType}")
    private String hyLoanType;

    @Value("${cybk.hy.merchant.code}")
    private String hyMerchantCode;

    @Value("${cybk.hy.merchant.shop}")
    private String hyMerchantShop;

    @Value("${cybk.hy.merchant.terminal}")
    private String hyMerchantTerminal;

    /**
     * 业务数据 加密密钥
     */
    @Value("${cybk.hy.content.key}")
    private String hyContentKey;

    /**
     * MD5后的签名 加密密钥
     */
    @Value("${cybk.hy.sign.key}")
    private String hySignKey;

    @Value("${cybk.hy.oss.bucket}")
    private String hyCybkOssBucket;

    @Value("${cybk.hy.repay.start}")
    private String hyRepayStartTime;

    @Value("${cybk.hy.repay.end}")
    private String hyRepayEndTime;

    @Value("${cybk.hy.guarantee.company.address}")
    private String hyGuaranteeCompanyAddress;

    @Value("${cybk.hy.repay.channel}")
    private String hyRepayChannel;

    @Value("${cybk.hy.sendImage.retries}")
    private Integer hyRetries;

    @Value("${cybk.hy.callBack.url}")
    private String hyCallBackUrl;

    @Value("${cybk.http.scheme}")
    private String scheme;

    @Value("${cybk.http.host}")
    private String httpHost;

    @Value("${cybk.http.port}")
    private int httpPort;

    public String getScheme() {
        return scheme;
    }

    public void setScheme( String scheme ) {
        this.scheme = scheme;
    }

    public String getHttpHost() {
        return httpHost;
    }

    public void setHttpHost( String httpHost ) {
        this.httpHost = httpHost;
    }

    public int getHttpPort() {
        return httpPort;
    }

    public void setHttpPort( int httpPort ) {
        this.httpPort = httpPort;
    }

    public Integer getRetries( GuaranteeCompany guaranteeCompany ) {
        return switch (guaranteeCompany) {
            case CJRD -> retries;
            case HYRD -> hyRetries;
            default -> retries;
        };
    }

    public String getRepayChannel(GuaranteeCompany guaranteeCompany) {
        return switch (guaranteeCompany) {
            case CJRD -> repayChannel;
            case HYRD -> hyRepayChannel;
            default -> repayChannel;
        };
    }

    public String getGuaranteeCompanyAddress(GuaranteeCompany guaranteeCompany) {
        return switch (guaranteeCompany) {
            case CJRD -> guaranteeCompanyAddress;
            case HYRD -> hyGuaranteeCompanyAddress;
            default -> guaranteeCompanyAddress;
        };
    }

    public String getChannelId(GuaranteeCompany guaranteeCompany) {
        return switch (guaranteeCompany) {
            case CJRD -> channelId;
            case HYRD -> hyChannelId;
            default -> channelId;
        };
    }

    public String getShortCode(GuaranteeCompany guaranteeCompany) {
        return switch (guaranteeCompany) {
            case CJRD -> shortCode;
            case HYRD -> hyShortCode;
            default -> shortCode;
        };
    }

    public String getLoanType(GuaranteeCompany guaranteeCompany) {
        //todo 定时任务改造
        return switch (guaranteeCompany) {
            case CJRD -> loanType;
            case HYRD -> hyLoanType;
            default -> loanType;
        };
    }

    public String getMerchantCode(GuaranteeCompany guaranteeCompany) {
        return switch (guaranteeCompany) {
            case CJRD -> merchantCode;
            case HYRD -> hyMerchantCode;
            default -> merchantCode;
        };
    }

    public String getMerchantShop(GuaranteeCompany guaranteeCompany) {
        return switch (guaranteeCompany) {
            case CJRD -> merchantShop;
            case HYRD -> hyMerchantShop;
            default -> merchantShop;
        };
    }

    public String getMerchantTerminal(GuaranteeCompany guaranteeCompany) {
        return switch (guaranteeCompany) {
            case CJRD -> merchantTerminal;
            case HYRD -> hyMerchantTerminal;
            default -> merchantTerminal;
        };
    }

    public String getCybkServerUrl(GuaranteeCompany guaranteeCompany) {
        return switch (guaranteeCompany) {
            case CJRD -> cybkServerUrl;
            case HYRD -> cybkServerUrl;
            default -> cybkServerUrl;
        };
    }

    public String getSignKey(GuaranteeCompany guaranteeCompany) {
        return switch (guaranteeCompany) {
            case CJRD -> signKey;
            case HYRD -> hySignKey;
            default -> signKey;
        };
    }

    public String getCybkOssBucket(GuaranteeCompany guaranteeCompany) {
        return switch (guaranteeCompany) {
            case CJRD -> cybkOssBucket;
            case HYRD -> hyCybkOssBucket;
            default -> cybkOssBucket;
        };
    }

    public String getCreditStartTime() {
        return creditStartTime;
    }

    public String getCreditEndTime() {
        return creditEndTime;
    }

    public String getLoanStartTime() {
        return loanStartTime;
    }

    public String getLoanEndTime() {
        return loanEndTime;
    }

    public String getRepayStartTime(GuaranteeCompany guaranteeCompany) {
        return switch (guaranteeCompany) {
            case CJRD -> repayStartTime;
            case HYRD -> hyRepayStartTime;
            default -> repayStartTime;
        };
    }

    public String getRepayEndTime(GuaranteeCompany guaranteeCompany) {
        return switch (guaranteeCompany) {
            case CJRD -> repayEndTime;
            case HYRD -> hyRepayEndTime;
            default -> repayEndTime;
        };
    }

    public String getAppid(GuaranteeCompany guaranteeCompany) {
        return switch (guaranteeCompany) {
            case CJRD -> appid;
            case HYRD -> hyAppid;
            default -> appid;
        };
    }

    public String getChannel(GuaranteeCompany guaranteeCompany) {
        return switch (guaranteeCompany) {
            case CJRD -> channel;
            case HYRD -> hyChannel;
            default -> channel;
        };
    }

    public String getContentKey(GuaranteeCompany guaranteeCompany) {
        return switch (guaranteeCompany) {
            case CJRD -> contentKey;
            case HYRD -> hyContentKey;
            default -> contentKey;
        };
    }

    public String getCallBackUrl(GuaranteeCompany guaranteeCompany) {
        return switch (guaranteeCompany) {
            case CJRD -> callBackUrl;
            case HYRD -> hyCallBackUrl;
            default -> callBackUrl;
        };
    }
}
