# 授信接口日志存储方案

## 📋 方案概述

本方案采用**AOP切面 + 数据库存储**的方式，实现对授信相关接口的请求响应数据自动记录。

## 🎯 技术选型

### 推荐方案：AOP切面拦截
- **无侵入性**：不需要修改现有业务代码
- **统一管理**：集中处理所有授信接口的日志记录
- **易于维护**：切面逻辑独立，便于后续扩展
- **性能友好**：异步处理，不影响主业务流程

### 其他方案对比
| 方案 | 优点 | 缺点 | 适用场景 |
|------|------|------|----------|
| **AOP切面** ✅ | 无侵入、统一管理、易维护 | 需要Spring支持 | **推荐** |
| 拦截器 | 可拦截HTTP请求 | 粒度粗、难区分业务 | 全局请求记录 |
| 过滤器 | 最早拦截 | 无法获取方法信息 | 请求预处理 |
| 手动埋点 | 灵活控制 | 代码侵入性强 | 特殊场景 |

## 🗄️ 数据库表设计

### credit_api_log - 授信接口日志表

```sql
CREATE TABLE `credit_api_log` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `trace_id` varchar(64) DEFAULT NULL COMMENT '链路追踪ID',
  `flow_channel` varchar(32) DEFAULT NULL COMMENT '流量渠道',
  `bank_channel` varchar(32) DEFAULT NULL COMMENT '银行渠道',
  `api_name` varchar(100) NOT NULL COMMENT '接口名称',
  `method_name` varchar(100) NOT NULL COMMENT '方法名',
  `class_name` varchar(200) NOT NULL COMMENT '类名',
  `request_url` varchar(500) DEFAULT NULL COMMENT '请求URL',
  `http_method` varchar(10) DEFAULT NULL COMMENT 'HTTP方法',
  `request_params` longtext COMMENT '请求参数(JSON)',
  `response_data` longtext COMMENT '响应数据(JSON)',
  `is_success` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否成功(1-成功,0-失败)',
  `error_message` text COMMENT '错误信息',
  `exception_detail` longtext COMMENT '异常详情',
  `execution_time` bigint DEFAULT NULL COMMENT '执行耗时(毫秒)',
  `request_ip` varchar(50) DEFAULT NULL COMMENT '请求IP',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `business_id` varchar(64) DEFAULT NULL COMMENT '业务ID(订单号/授信ID等)',
  `cert_no` varchar(32) DEFAULT NULL COMMENT '身份证号(脱敏)',
  `mobile` varchar(20) DEFAULT NULL COMMENT '手机号(脱敏)',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `revision` int DEFAULT '0' COMMENT '乐观锁版本号',
  `created_by` varchar(50) DEFAULT 'system' COMMENT '创建人',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(50) DEFAULT 'system' COMMENT '更新人',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_trace_id` (`trace_id`),
  KEY `idx_flow_channel` (`flow_channel`),
  KEY `idx_bank_channel` (`bank_channel`),
  KEY `idx_api_name` (`api_name`),
  KEY `idx_business_id` (`business_id`),
  KEY `idx_cert_no` (`cert_no`),
  KEY `idx_created_time` (`created_time`),
  KEY `idx_is_success` (`is_success`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='授信接口调用日志表';
```

### 字段说明
- **trace_id**: 链路追踪ID，便于问题排查
- **flow_channel/bank_channel**: 区分不同的流量方和银行
- **api_name**: 接口名称，如"授信申请"、"授信查询"
- **request_params/response_data**: JSON格式存储完整的请求响应数据
- **is_success**: 快速筛选成功失败的调用
- **execution_time**: 性能监控指标
- **business_id**: 关联业务主键，便于业务查询
- **cert_no/mobile**: 脱敏后的用户标识，便于用户维度查询

## 🏗️ 实现架构

### 1. 实体类设计
```java
@Entity
@Table(name = "credit_api_log")
public class CreditApiLog extends BaseEntity {
    // 继承BaseEntity获得审计字段
    // 具体字段定义...
}
```

### 2. AOP切面实现
```java
@Aspect
@Component
public class CreditApiLogAspect {
    
    @Around("@annotation(CreditApiLog) || execution(* *..*CreditService.*(..))")
    public Object logAround(ProceedingJoinPoint joinPoint) {
        // 记录请求开始时间
        // 执行目标方法
        // 记录响应和异常
        // 异步保存日志
    }
}
```

### 3. 注解驱动
```java
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface CreditApiLog {
    String value() default ""; // 接口描述
    boolean logRequest() default true; // 是否记录请求参数
    boolean logResponse() default true; // 是否记录响应数据
}
```

## 🔧 实现特性

### 1. 数据脱敏
- 身份证号：保留前6位和后4位，中间用*替代
- 手机号：保留前3位和后4位，中间用*替代
- 敏感字段：自动识别并脱敏处理

### 2. 异步处理
- 使用`@Async`异步保存日志，不影响主业务性能
- 配置独立的线程池处理日志任务

### 3. 异常处理
- 日志记录失败不影响主业务流程
- 提供降级策略，确保系统稳定性

### 4. 性能优化
- 大数据量时采用批量插入
- 配置合适的数据库索引
- 定期清理历史数据

## 📈 扩展功能

### 1. 监控告警
- 接口调用失败率监控
- 响应时间异常告警
- 调用量突增告警

### 2. 数据分析
- 接口调用统计报表
- 性能趋势分析
- 错误分布分析

### 3. 查询接口
- 按时间范围查询
- 按业务ID查询
- 按用户维度查询
- 按接口类型查询

## 🚀 部署建议

### 1. 配置管理
```yaml
credit:
  api:
    log:
      enabled: true # 是否启用日志记录
      async: true # 是否异步处理
      max-request-size: 10240 # 最大请求参数长度
      max-response-size: 10240 # 最大响应数据长度
      retention-days: 90 # 数据保留天数
```

### 2. 数据库优化
- 按月分表存储，提高查询性能
- 配置读写分离，查询走从库
- 定期归档历史数据

### 3. 监控指标
- 日志记录成功率
- 异步队列积压情况
- 数据库存储空间使用率

## 📝 使用示例

### 1. 注解使用
```java
@CreditApiLog("授信申请接口")
public CreditResultVo apply(CreditApplyVo apply) {
    // 业务逻辑
}
```

### 2. 查询日志
```java
// 按业务ID查询
List<CreditApiLog> logs = creditApiLogService.findByBusinessId(creditId);

// 按时间范围查询
Page<CreditApiLog> logs = creditApiLogService.findByTimeRange(startTime, endTime);
```

## 🎯 总结

本方案具有以下优势：
1. **无侵入性**：不需要修改现有业务代码
2. **高性能**：异步处理，不影响主业务
3. **易扩展**：支持灵活的配置和功能扩展
4. **易维护**：统一的日志管理和查询接口
5. **高可用**：完善的异常处理和降级策略

通过这个方案，可以全面记录授信接口的调用情况，为问题排查、性能优化和业务分析提供强有力的数据支撑。
