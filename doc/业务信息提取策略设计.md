# 业务信息提取策略设计

## 🎯 问题分析

不同流量方的授信申请请求参数结构不同，关键业务字段的字段名也不一样，需要设计通用的业务信息提取策略。

## 📊 字段映射分析

### 关键业务字段对比

| 业务字段 | 绿信(ApprovalRequest) | 拍拍(CreditApplyRequest) | 分期乐(FenQiLeCreditApplyRequest) |
|----------|----------------------|-------------------------|----------------------------------|
| **身份证号** | `idCard` | `idNo` | `idCardNo` |
| **手机号** | `phone` | `mobileNo` | `mobileNo` |
| **姓名** | `name` | `custName` | `name` |
| **订单号/流水号** | `partnerUserId` | `loanReqNo` | `creditApplyId` |
| **申请金额** | `creditAmount` | `creditAmount` | `creditAmount` |

## 🏗️ 解决方案设计

### 方案1：策略模式 + 字段映射（推荐）

#### 1.1 定义业务信息提取接口
```java
public interface BusinessInfoExtractor {
    /**
     * 提取业务信息
     */
    BusinessInfo extractBusinessInfo(Object requestObject);
    
    /**
     * 判断是否支持该请求类型
     */
    boolean supports(Object requestObject);
}
```

#### 1.2 定义业务信息对象
```java
public class BusinessInfo {
    private String certNo;        // 身份证号
    private String mobile;        // 手机号
    private String name;          // 姓名
    private String businessId;    // 业务ID(订单号/流水号)
    private String creditAmount;  // 申请金额
    private String flowChannel;   // 流量渠道
    
    // getter/setter...
}
```

#### 1.3 实现具体的提取器

**绿信业务信息提取器**
```java
@Component
public class LvxinBusinessInfoExtractor implements BusinessInfoExtractor {
    
    @Override
    public BusinessInfo extractBusinessInfo(Object requestObject) {
        if (!(requestObject instanceof ApprovalRequest)) {
            return null;
        }
        
        ApprovalRequest request = (ApprovalRequest) requestObject;
        BusinessInfo businessInfo = new BusinessInfo();
        businessInfo.setCertNo(request.getIdCard());
        businessInfo.setMobile(request.getPhone());
        businessInfo.setName(request.getName());
        businessInfo.setBusinessId(request.getPartnerUserId());
        businessInfo.setCreditAmount(request.getCreditAmount().toString());
        businessInfo.setFlowChannel("LVXIN");
        
        return businessInfo;
    }
    
    @Override
    public boolean supports(Object requestObject) {
        return requestObject instanceof ApprovalRequest;
    }
}
```

**拍拍业务信息提取器**
```java
@Component
public class PpdBusinessInfoExtractor implements BusinessInfoExtractor {
    
    @Override
    public BusinessInfo extractBusinessInfo(Object requestObject) {
        if (!(requestObject instanceof CreditApplyRequest)) {
            return null;
        }
        
        CreditApplyRequest request = (CreditApplyRequest) requestObject;
        BusinessInfo businessInfo = new BusinessInfo();
        businessInfo.setCertNo(request.getIdNo());
        businessInfo.setMobile(request.getMobileNo());
        businessInfo.setName(request.getCustName());
        businessInfo.setBusinessId(request.getLoanReqNo());
        businessInfo.setCreditAmount(request.getCreditAmount().toString());
        businessInfo.setFlowChannel("PPD");
        
        return businessInfo;
    }
    
    @Override
    public boolean supports(Object requestObject) {
        return requestObject instanceof CreditApplyRequest;
    }
}
```

**分期乐业务信息提取器**
```java
@Component
public class FenQiLeBusinessInfoExtractor implements BusinessInfoExtractor {
    
    @Override
    public BusinessInfo extractBusinessInfo(Object requestObject) {
        if (!(requestObject instanceof FenQiLeCreditApplyRequest)) {
            return null;
        }
        
        FenQiLeCreditApplyRequest request = (FenQiLeCreditApplyRequest) requestObject;
        BusinessInfo businessInfo = new BusinessInfo();
        businessInfo.setCertNo(request.getIdCardNo());
        businessInfo.setMobile(request.getMobileNo());
        businessInfo.setName(request.getName());
        businessInfo.setBusinessId(request.getCreditApplyId());
        businessInfo.setCreditAmount(request.getCreditAmount().toString());
        businessInfo.setFlowChannel("FENQILE");
        
        return businessInfo;
    }
    
    @Override
    public boolean supports(Object requestObject) {
        return requestObject instanceof FenQiLeCreditApplyRequest;
    }
}
```

#### 1.4 业务信息提取管理器
```java
@Component
public class BusinessInfoExtractorManager {
    
    private final List<BusinessInfoExtractor> extractors;
    
    public BusinessInfoExtractorManager(List<BusinessInfoExtractor> extractors) {
        this.extractors = extractors;
    }
    
    /**
     * 提取业务信息
     */
    public BusinessInfo extractBusinessInfo(Object requestObject) {
        for (BusinessInfoExtractor extractor : extractors) {
            if (extractor.supports(requestObject)) {
                return extractor.extractBusinessInfo(requestObject);
            }
        }
        return new BusinessInfo(); // 返回空的业务信息
    }
}
```

### 方案2：注解 + 反射（备选方案）

#### 2.1 定义字段映射注解
```java
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface BusinessField {
    BusinessFieldType value();
}

public enum BusinessFieldType {
    CERT_NO,    // 身份证号
    MOBILE,     // 手机号
    NAME,       // 姓名
    BUSINESS_ID, // 业务ID
    CREDIT_AMOUNT // 申请金额
}
```

#### 2.2 在请求对象上添加注解
```java
public class ApprovalRequest {
    @BusinessField(BusinessFieldType.CERT_NO)
    private String idCard;
    
    @BusinessField(BusinessFieldType.MOBILE)
    private String phone;
    
    @BusinessField(BusinessFieldType.NAME)
    private String name;
    
    @BusinessField(BusinessFieldType.BUSINESS_ID)
    private String partnerUserId;
    
    @BusinessField(BusinessFieldType.CREDIT_AMOUNT)
    private BigDecimal creditAmount;
    
    // 其他字段...
}
```

#### 2.3 反射提取器
```java
@Component
public class ReflectionBusinessInfoExtractor {
    
    public BusinessInfo extractBusinessInfo(Object requestObject) {
        BusinessInfo businessInfo = new BusinessInfo();
        
        Field[] fields = requestObject.getClass().getDeclaredFields();
        for (Field field : fields) {
            BusinessField annotation = field.getAnnotation(BusinessField.class);
            if (annotation != null) {
                field.setAccessible(true);
                try {
                    Object value = field.get(requestObject);
                    setBusinessInfoField(businessInfo, annotation.value(), value);
                } catch (IllegalAccessException e) {
                    // 处理异常
                }
            }
        }
        
        return businessInfo;
    }
    
    private void setBusinessInfoField(BusinessInfo businessInfo, BusinessFieldType fieldType, Object value) {
        if (value == null) return;
        
        switch (fieldType) {
            case CERT_NO:
                businessInfo.setCertNo(value.toString());
                break;
            case MOBILE:
                businessInfo.setMobile(value.toString());
                break;
            case NAME:
                businessInfo.setName(value.toString());
                break;
            case BUSINESS_ID:
                businessInfo.setBusinessId(value.toString());
                break;
            case CREDIT_AMOUNT:
                businessInfo.setCreditAmount(value.toString());
                break;
        }
    }
}
```

## 🎯 推荐方案

**推荐使用方案1（策略模式）**，原因：

### 优点
1. **类型安全**：编译时就能发现类型错误
2. **性能好**：不需要反射，运行时性能更好
3. **易维护**：每个流量方的提取逻辑独立，便于维护
4. **易扩展**：新增流量方只需要实现新的提取器
5. **无侵入**：不需要修改现有的请求对象

### 缺点
1. **代码量稍多**：需要为每个流量方实现提取器

## 🔧 在AOP中的使用

```java
@Aspect
@Component
public class CreditApiLogAspect {
    
    @Autowired
    private BusinessInfoExtractorManager businessInfoExtractorManager;
    
    @Around("creditApplyPointcut()")
    public Object logAround(ProceedingJoinPoint joinPoint) {
        try {
            // 1. 获取请求参数（第一个参数是请求对象）
            Object[] args = joinPoint.getArgs();
            Object requestObject = args[0];
            
            // 2. 提取业务信息
            BusinessInfo businessInfo = businessInfoExtractorManager.extractBusinessInfo(requestObject);
            
            // 3. 执行目标方法
            Object result = joinPoint.proceed();
            
            // 4. 保存日志
            saveLog(requestObject, result, businessInfo, true, null);
            
            return result;
            
        } catch (Exception e) {
            // 异常处理
            throw e;
        }
    }
}
```

这样就能优雅地解决不同流量方字段名不同的问题，既保证了代码的可维护性，又具有良好的扩展性。
