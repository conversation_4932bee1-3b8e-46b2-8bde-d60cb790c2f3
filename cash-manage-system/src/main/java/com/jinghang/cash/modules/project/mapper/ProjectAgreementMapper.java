package com.jinghang.cash.modules.project.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jinghang.cash.modules.project.domain.ProjectAgreement;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 协议模板配置Mapper
 *
 * @Author: Lior
 * @CreateTime: 2025/8/25 09:40
 */
@Mapper
public interface ProjectAgreementMapper extends BaseMapper<ProjectAgreement> {

    /**
     * 根据项目代码、流程贷款阶段和合同模板类型获取项目协议
     *
     * @param projectCode 项目编码
     * @param flowLoanStage 资产方合同签署阶段
     * @param capitalLoanStage 资金方合同签署阶段
     * @param contractTemplateType 合同模板类型
     * @return 项目协议
     */
    ProjectAgreement selectByStageAndType(@Param("projectCode") String projectCode,
                                          @Param("flowLoanStage") String flowLoanStage,
                                          @Param("capitalLoanStage") String capitalLoanStage,
                                          @Param("contractTemplateType") String contractTemplateType);

    /**
     * 根据项目代码和是否退回流程获取项目协议列表
     *
     * @param projectCodes 项目编码列表
     * @param isReturnToFlow 是否回传流量方
     * @param isReturnToCapital 是否回传资金方
     * @return 项目协议列表
     */
    List<ProjectAgreement> selectByReturnStatus(@Param("projectCodes") List<String> projectCodes,
                                                @Param("isReturnToFlow") String isReturnToFlow,
                                                @Param("isReturnToCapital") String isReturnToCapital);

    /**
     * 根据项目代码和流程贷款阶段获取项目协议列表
     *
     * @param projectCode 项目编码
     * @param flowLoanStage 资产方合同签署阶段
     * @param capitalLoanStage 资金方合同签署阶段
     * @return 项目协议列表
     */
    List<ProjectAgreement> selectByStage(@Param("projectCode") String projectCode,
                                         @Param("flowLoanStage") String flowLoanStage,
                                         @Param("capitalLoanStage") String capitalLoanStage);
}
