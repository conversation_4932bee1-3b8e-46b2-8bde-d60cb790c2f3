package com.jinghang.cash.api;

import com.jinghang.cash.api.dto.ProjectAgreementDto;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 项目协议API接口
 *
 * @Author: Lior
 * @CreateTime: 2025/8/25 14:00
 */
public interface ProjectAgreementApiService {

    /**
     * 根据项目代码、流程贷款阶段和合同模板类型获取项目协议
     *
     * @param projectCode 项目编码
     * @param flowLoanStage 资产方合同签署阶段
     * @param capitalLoanStage 资金方合同签署阶段
     * @param contractTemplateType 合同模板类型
     * @return 项目协议
     */
    @PostMapping("/queryByStageAndType")
    ProjectAgreementDto getByStageAndType(@RequestParam("projectCode") String projectCode,
                                          @RequestParam(value = "flowLoanStage", required = false) String flowLoanStage,
                                          @RequestParam(value = "capitalLoanStage", required = false) String capitalLoanStage,
                                          @RequestParam("contractTemplateType") String contractTemplateType);

    /**
     * 根据项目代码和是否退回流程获取项目协议列表
     *
     * @param projectCodes 项目编码列表
     * @param isReturnToFlow 是否回传流量方
     * @param isReturnToCapital 是否回传资金方
     * @return 项目协议列表
     */
    @PostMapping("/queryByReturnStatus")
    List<ProjectAgreementDto> getByReturnStatus(@RequestParam("projectCodes") List<String> projectCodes,
                                                @RequestParam(value = "isReturnToFlow", required = false) String isReturnToFlow,
                                                @RequestParam(value = "isReturnToCapital", required = false) String isReturnToCapital);

    /**
     * 根据项目代码和流程贷款阶段获取项目协议列表
     *
     * @param projectCode 项目编码
     * @param flowLoanStage 资产方合同签署阶段
     * @param capitalLoanStage 资金方合同签署阶段
     * @return 项目协议列表
     */
    @PostMapping("/queryByStage")
    List<ProjectAgreementDto> getByStage(@RequestParam("projectCode") String projectCode,
                                         @RequestParam(value = "flowLoanStage", required = false) String flowLoanStage,
                                         @RequestParam(value = "capitalLoanStage", required = false) String capitalLoanStage);
}
